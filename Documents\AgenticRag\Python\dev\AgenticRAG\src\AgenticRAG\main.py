from imports import *

from threading import Thread
from pathlib import Path
from sys import gettrace

from etc.setup import init, late_init
from os.path import exists
import tools.processing.embeddings as embeddings
import etc.parsers as parsers

from main_loop import exec_main_loop
from endpoints.oauth_endpoint import OAuth2Verifier

async def mainFunc():
    data_subfolder = ""#input("Specify data subfolder (blank?): ")

    if Globals.is_docker():
        DATA_DIR = Path("/")
        PERSIST_DIR = Path("/")
        debug_token = await OAuth2Verifier.get_token("debug")
        Globals.set_debug(debug_token)
    else:
        DATA_DIR = "" / BASE_DIR().parent.parent
        PERSIST_DIR = "" / BASE_DIR().parent.parent
    DATA_DIR = DATA_DIR / (("_DATA_RAW/" + data_subfolder) if (data_subfolder == "") == False else "_DATA_RAW")
    PERSIST_DIR = PERSIST_DIR / (("_DATA_EMBEDDED/" + data_subfolder) if (data_subfolder == "") == False else "_DATA_EMBEDDED")
    print(f"Data_dir: {str(DATA_DIR)}")
    print(f"Persist dir: {str(PERSIST_DIR)}")
    
    if not exists(str(PERSIST_DIR / "docstore.json")):
        newProject = True
        if Globals.is_docker() == True:
            print("Moving .client-env into .env")
            from dotenv import dotenv_values, set_key

            # Load both .env files
            tmp_env_path = "/tmp/.client-env"
            main_env_path = "/app/.env"
            
            # Load values as dictionaries
            tmp_env = dotenv_values(tmp_env_path)
            main_env = dotenv_values(main_env_path)

            # Update main env with tmp env values
            for key, value in tmp_env.items():
                set_key(main_env_path, key, value)

    else:
        newProject = False
    from dotenv import load_dotenv
    if Globals.get_debug():
        from os import getcwd
        load_dotenv(dotenv_path=getcwd() + "/dev.env")
    load_dotenv()
    
    await init()

    if newProject:
        index = await embeddings.generateEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
    else:
        index = await embeddings.loadEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
    Globals.set_index(index)
    # GETINDEX can be safely used from this point onwards
    await late_init()

    # ---------------------------- START OF TRY-OUT CODES ----------------------------
    # ----------------------------------- TRY-OUT 1 ----------------------------------
    if (False):
        from managers.manager_meltano import MeltanoManager
        target = "target-postgres"
        if not Globals.is_docker():
            target += "-local"
        etc.helper_functions.call_network_docker("meltano", f"run tap-airtable {target}")
        await MeltanoManager.ConvertSQLToVectorStore(None)
        #exit()
    # ----------------------------------- TRY-OUT 1 ----------------------------------

    # ----------------------------------- TRY-OUT 2 ----------------------------------
    if (False):
        exit()
    # ----------------------------------- TRY-OUT 2 ----------------------------------

    # ----------------------------------- TRY-OUT 3 ----------------------------------
    if (False):
        from reasoner.reasoner_old.pydantic_model import pydantic_test
        pydantic_test()
        #exit()
    # ----------------------------------- TRY-OUT 3 ----------------------------------
    # ----------------------------- END OF TRY-OUT CODES -----------------------------

    await exec_main_loop()
    #Thread(target=lambda: await exec_main_loop(),daemon=True).start()
    # main.py should always stay below 64 lines of code ideally to maintain startup performance
