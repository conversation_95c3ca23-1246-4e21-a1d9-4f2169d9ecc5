#!/usr/bin/env python3
"""
Debug script to test crawler data storage and retrieval.
This script helps identify issues in the data flow between crawler ingestion and question answering.
"""

import asyncio
from imports import *
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager
from tools.processing.embeddings import loadEmbedding
from globals import Globals
from etc.setup import init
from llama_index.core.vector_stores import MetadataFilters

async def test_qdrant_direct_access():
    """Test direct access to Qdrant to see what data is stored"""
    print("=" * 60)
    print("TESTING DIRECT QDRANT ACCESS")
    print("=" * 60)
    
    try:
        client = QDrantManager.GetAsyncClient()
        
        # Get collection info
        collection_info = await client.get_collection("mainCollection")
        print(f"Collection info: {collection_info}")
        
        # Get some sample points
        points = await client.scroll(
            collection_name="mainCollection",
            limit=5,
            with_payload=True,
            with_vectors=True
        )
        
        print(f"\nFound {len(points[0])} points in collection")
        
        for i, point in enumerate(points[0]):
            print(f"\n--- Point {i+1} ---")
            print(f"ID: {point.id}")
            print(f"Payload keys: {list(point.payload.keys()) if point.payload else 'None'}")
            
            # Check for crawler-specific metadata
            if point.payload:
                if 'file_path' in point.payload:
                    print(f"File path: {point.payload['file_path']}")
                if 'file_type' in point.payload:
                    print(f"File type: {point.payload['file_type']}")
                if '_node_content' in point.payload:
                    content = point.payload['_node_content']
                    print(f"Content preview: {content[:100]}..." if len(content) > 100 else f"Content: {content}")
            
            # Check vector fields
            if point.vector:
                print(f"Vector fields: {list(point.vector.keys())}")
                
        return len(points[0]) > 0
        
    except Exception as e:
        print(f"Error accessing Qdrant directly: {e}")
        return False

async def test_index_retrieval():
    """Test retrieval through the LlamaIndex system"""
    print("\n" + "=" * 60)
    print("TESTING INDEX RETRIEVAL")
    print("=" * 60)
    
    try:
        # Load the index
        index = Globals.get_index()
        if not index:
            print("No index found in Globals, trying to load...")
            from etc.helper_functions import BASE_DIR
            PERSIST_DIR = BASE_DIR() / "storage"
            index = await loadEmbedding(PERSIST_DIR)
            Globals.set_index(index)
        
        print("Index loaded successfully")
        
        # Test a simple query
        test_query = "test content"
        print(f"\nTesting query: '{test_query}'")
        
        # Create query engine with hybrid search
        filters = MetadataFilters(filters=[])
        query_engine = index.as_query_engine(
            filters=filters,
            response_mode="no_text",
            similarity_top_k=10,
            sparse_top_k=7,
            vector_store_query_mode="hybrid"
        )
        
        # Execute query
        response = await query_engine.aquery(test_query)
        
        # Check results
        if hasattr(response, 'source_nodes'):
            nodes = response.source_nodes
            print(f"Retrieved {len(nodes)} nodes")
            
            for i, node in enumerate(nodes[:3]):  # Show first 3 nodes
                print(f"\n--- Node {i+1} ---")
                if hasattr(node, 'text'):
                    print(f"Text preview: {node.text[:100]}...")
                if hasattr(node, 'metadata'):
                    print(f"Metadata: {node.metadata}")
                if hasattr(node, 'score'):
                    print(f"Score: {node.score}")
                    
            return len(nodes) > 0
        else:
            print("No source_nodes found in response")
            return False
            
    except Exception as e:
        print(f"Error testing index retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_crawler_specific_query():
    """Test a query that should match crawler data"""
    print("\n" + "=" * 60)
    print("TESTING CRAWLER-SPECIFIC QUERY")
    print("=" * 60)
    
    try:
        index = Globals.get_index()
        
        # Query for web page content
        test_queries = [
            "web page content",
            "website information", 
            "crawled data",
            "http",
            "url"
        ]
        
        for query in test_queries:
            print(f"\nTesting query: '{query}'")
            
            query_engine = index.as_query_engine(
                response_mode="no_text",
                similarity_top_k=5,
                sparse_top_k=5,
                vector_store_query_mode="hybrid"
            )
            
            response = await query_engine.aquery(query)
            
            if hasattr(response, 'source_nodes') and response.source_nodes:
                print(f"Found {len(response.source_nodes)} nodes")
                
                # Check if any nodes are from crawler
                crawler_nodes = 0
                for node in response.source_nodes:
                    if hasattr(node, 'metadata') and node.metadata:
                        if (node.metadata.get('file_type') == 'web_page' or 
                            'http' in str(node.metadata.get('file_path', ''))):
                            crawler_nodes += 1
                            print(f"  - Found crawler node: {node.metadata.get('file_path', 'Unknown')}")
                
                print(f"Crawler nodes found: {crawler_nodes}/{len(response.source_nodes)}")
                
                if crawler_nodes > 0:
                    return True
            else:
                print("No nodes found")
        
        return False
        
    except Exception as e:
        print(f"Error testing crawler-specific query: {e}")
        return False

async def main():
    """Main debug function"""
    print("CRAWLER RETRIEVAL DEBUG SCRIPT")
    print("=" * 60)
    
    # Initialize the system
    try:
        await init()
        print("System initialized successfully")
    except Exception as e:
        print(f"Error initializing system: {e}")
        return
    
    # Load index if not already loaded
    if not Globals.get_index():
        try:
            from etc.helper_functions import BASE_DIR
            PERSIST_DIR = BASE_DIR() / "storage"
            index = await loadEmbedding(PERSIST_DIR)
            Globals.set_index(index)
            print("Index loaded and set in Globals")
        except Exception as e:
            print(f"Error loading index: {e}")
            return
    
    # Run tests
    results = {}
    
    results['qdrant_access'] = await test_qdrant_direct_access()
    results['index_retrieval'] = await test_index_retrieval()
    results['crawler_query'] = await test_crawler_specific_query()
    
    # Summary
    print("\n" + "=" * 60)
    print("DEBUG RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    if all(results.values()):
        print("\n🎉 All tests passed! Crawler data should be accessible.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        
        if not results['qdrant_access']:
            print("   - No data found in Qdrant. Run the crawler first.")
        if not results['index_retrieval']:
            print("   - Index retrieval failed. Check vector field configuration.")
        if not results['crawler_query']:
            print("   - Crawler data not found in queries. Check metadata structure.")

if __name__ == "__main__":
    asyncio.run(main())
