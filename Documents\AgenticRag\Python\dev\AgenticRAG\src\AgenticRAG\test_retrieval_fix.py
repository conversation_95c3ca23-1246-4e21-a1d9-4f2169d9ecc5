#!/usr/bin/env python3
"""
Test script to verify that the retrieval system works with crawler data after the vector field fix.
"""

import asyncio
from imports import *
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.core.vector_stores import MetadataFilters
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager

async def test_corrected_retrieval():
    """Test retrieval with the corrected vector field names"""
    print("=" * 60)
    print("TESTING CORRECTED RETRIEVAL CONFIGURATION")
    print("=" * 60)
    
    try:
        # Create vector store with corrected field names
        vector_store = QdrantVectorStore(
            client=QDrantManager.GetClient(), 
            aclient=QDrantManager.GetAsyncClient(), 
            collection_name="mainCollection",
            enable_hybrid=True,
            sparse_encoder=ZairaSettings.sparse_embed_model,
            batch_size=20,
            vector_field_name="text-dense",  # Corrected field name
            sparse_vector_field_name="text-sparse"  # Corrected field name
        )
        
        print("✓ Vector store created with corrected field names")
        
        # Create storage context
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        
        # Create index from vector store
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store,
            storage_context=storage_context,
            embed_model=ZairaSettings.OllamaSettings().embed_model
        )
        
        print("✓ Index created successfully")
        
        # Test queries that should match crawler data
        test_queries = [
            "ski equipment",
            "sports gear", 
            "snowboard",
            "h-gsports",
            "skiing"
        ]
        
        for query in test_queries:
            print(f"\n--- Testing query: '{query}' ---")
            
            # Create query engine
            query_engine = index.as_query_engine(
                response_mode="no_text",
                similarity_top_k=5,
                sparse_top_k=5,
                vector_store_query_mode="hybrid"
            )
            
            # Execute query
            response = await query_engine.aquery(query)
            
            if hasattr(response, 'source_nodes') and response.source_nodes:
                print(f"✓ Found {len(response.source_nodes)} nodes")
                
                # Check for crawler data
                crawler_nodes = 0
                for node in response.source_nodes:
                    if hasattr(node, 'metadata') and node.metadata:
                        file_type = node.metadata.get('file_type', '')
                        file_path = node.metadata.get('file_path', '')
                        
                        if file_type == 'web_page' or 'http' in str(file_path):
                            crawler_nodes += 1
                            print(f"  ✓ Crawler node: {file_path}")
                            
                            # Show content preview
                            if hasattr(node, 'text') and node.text:
                                content_preview = node.text[:100].replace('\n', ' ')
                                print(f"    Content: {content_preview}...")
                
                if crawler_nodes > 0:
                    print(f"✅ SUCCESS: Found {crawler_nodes} crawler nodes for query '{query}'")
                    return True
                else:
                    print(f"⚠️  No crawler nodes found for query '{query}'")
            else:
                print(f"❌ No nodes found for query '{query}'")
        
        return False
        
    except Exception as e:
        print(f"❌ Error in retrieval test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rag_search_tool():
    """Test the actual RAG search tool that's used in the system"""
    print("\n" + "=" * 60)
    print("TESTING RAG SEARCH TOOL")
    print("=" * 60)
    
    try:
        # Import the RAG search tool
        from tools.inputs.task_retrieval import rag_search_tool
        
        # Test with a query that should match crawler data
        test_query = "ski equipment and sports gear"
        print(f"Testing RAG search tool with query: '{test_query}'")
        
        # Execute the RAG search tool
        result = await rag_search_tool(test_query)
        
        if result and "No relevant information found" not in result:
            print("✅ RAG search tool returned results!")
            print(f"Result preview: {result[:200]}...")
            return True
        else:
            print("❌ RAG search tool returned no results")
            print(f"Result: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing RAG search tool: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("TESTING RETRIEVAL SYSTEM AFTER VECTOR FIELD FIX")
    print("=" * 60)
    
    # Initialize required managers
    try:
        await QDrantManager.setup()
        await RetrievalManager.setup()
        print("✓ Managers initialized")
    except Exception as e:
        print(f"❌ Error initializing managers: {e}")
        return
    
    # Run tests
    results = {}
    
    results['corrected_retrieval'] = await test_corrected_retrieval()
    results['rag_search_tool'] = await test_rag_search_tool()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    if all(results.values()):
        print("\n🎉 SUCCESS! The vector field fix resolved the issue!")
        print("   Crawler data is now accessible through the retrieval system.")
        print("   Restart your main application to use the corrected configuration.")
    else:
        print("\n⚠️  Some tests failed. The issue may require additional investigation.")
        
        if not results['corrected_retrieval']:
            print("   - Direct retrieval failed. Check embedding model configuration.")
        if not results['rag_search_tool']:
            print("   - RAG search tool failed. Check tool implementation.")

if __name__ == "__main__":
    asyncio.run(main())
