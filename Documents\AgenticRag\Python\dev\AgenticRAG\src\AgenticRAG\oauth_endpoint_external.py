from imports import *

import aiohttp
import sys
from aiohttp import web
import asyncio
from urllib.parse import unquote
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("oauth_proxy")

def extract_query_params(request: web.Request) -> dict:
    raw_path = unquote(request.raw_path)  # decode any %3F, %3D, etc.

    # Look for the '?' character manually
    if '?' not in raw_path:
        return {}

    path_part, query_string = raw_path.split('?', 1)

    # Basic split and parse
    params = {}
    for pair in query_string.split('&'):
        if '=' in pair:
            key, value = pair.split('=', 1)
            params[key] = value
        elif pair:
            params[pair] = ''

    return params

async def handle_proxy(request: web.Request):
    try:
        params = extract_query_params(request)
        state = params.get("state")
        if state:
            split = state.split("-", 1)
            client_id = split[0]
            identifier = split[1]
            if client_id.lower() == "python":
                target_url = f"{Globals.get_endpoint_address()}{request.path_qs}"
            else:
                target_url = f"https://{client_id}.askzaira.com{request.path_qs}"

            logger.info(f"Proxying request for client '{client_id}' to {target_url}")

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8" />
                <title>Redirecting...</title>
                <script>
                    // Redirect immediately
                    window.location.href = "{target_url}";
                </script>
            </head>
            <body>
                <p>Token aanvragen... Klik <a href='{target_url}'>hier</a> als dit niet automatisch laadt.</p>
            </body>
            </html>
            """

            return web.Response(text=html_content, content_type='text/html')
    except Exception as e:
        etc.helper_functions.exception_triggered(e, "INIT")
        return web.HTTPInternalServerError(reason=str(e))
    raise web.HTTPNotFound(reason=f"No mapping found for path: {request.path}")

async def create_app():
    app = web.Application()
    app.router.add_route("*", "/{tail:.*}", handle_proxy)
    return app

runner = None
async def main():
    app = await create_app()
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 8084)
    await site.start()
    print(f"Starting OAuth External. Listening on {Globals.get_endpoint_address()}:8084.")

    # Run forever until interrupted
    if not Globals.get_debug():
        try:
            while True:
                await asyncio.sleep(3600)
        except KeyboardInterrupt:
            print("Shutting down server...")
        cleanup() # Cleaned called manually if it is debug

async def cleanup():
    await runner.cleanup()

if __name__ == "__main__":
    try:
        Globals.set_debug(False)
        asyncio.run(main())
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
