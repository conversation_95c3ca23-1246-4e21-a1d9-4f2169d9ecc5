from imports import *

from endpoints.oauth_endpoint import OAuth2App

class OAuth2Website(OAuth2App):
    def __init__(self, myname: str):
        super().__init__()
        self.setup(myname)

    def setup(self, myname):
        self.create_input(myname, "input", ["Site URL", "Sitemap (Optional)"]).set_commands(["crawl"])

    async def on_fail_return(self) -> str:
        # Wordt getoond zodra de koppeling gefaald is
        ret_html = await super().on_fail()
        ret_html += ""
        return ret_html

    async def on_fail_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_fail_execute klaar is
        ret_html = await super().on_fail_return()
        ret_html += ""
        return ret_html

    async def on_success_return(self) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return()
        ret_html += ""
        return ret_html

    async def on_success_execute(self) -> str:
        from endpoints.oauth_endpoint import OAuth2Verifier
        ret_html = await super().on_success_execute()
        # Crawl the user's website in case it changed
        site_url = await OAuth2Verifier.get_token("website")
        if site_url != "":
            print("Starting site-crawl")
            from subprocess import run as subprocess_run
            result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
            from inputs.crawler import Crawler
            # Initialize the crawler
            crawler = await Crawler.setup_async()
            sitemap = await OAuth2Verifier.get_token("website", "refresh_token")
            if sitemap == "":
                # Test with a single URL
                await crawler.crawl(site_url, is_sitemap=False)
            else:
                # Test with a sitemap
                sitemap_url = f"{site_url.rstrip('/')}/{sitemap}"
                await crawler.crawl(sitemap_url, is_sitemap=True, max_concurrent=5)
            # Clean up
            await crawler.close_crawler()
            print("Finished site-crawl")
        return ret_html

    async def on_success_return(self) -> str:
        ret_html = super().on_success()

        ret_html += ""
        return ret_html
