#!/usr/bin/env python3
"""
Simple debug script to test Qdrant data access without full system initialization.
"""

import asyncio
from qdrant_client import AsyncQdrantClient

async def test_qdrant_data():
    """Test direct access to Qdrant to see what data is stored"""
    print("=" * 60)
    print("TESTING QDRANT DATA ACCESS")
    print("=" * 60)
    
    try:
        # Connect to Qdrant
        client = AsyncQdrantClient(url="http://localhost:6333", timeout=300)
        
        # Get collection info
        try:
            collection_info = await client.get_collection("mainCollection")
            print(f"Collection exists: {collection_info.config.params.vectors}")
            print(f"Points count: {collection_info.points_count}")
        except Exception as e:
            print(f"Collection access error: {e}")
            return False
        
        # Get some sample points
        try:
            points = await client.scroll(
                collection_name="mainCollection",
                limit=10,
                with_payload=True,
                with_vectors=True
            )
            
            print(f"\nFound {len(points[0])} points in collection")
            
            crawler_points = 0
            for i, point in enumerate(points[0]):
                print(f"\n--- Point {i+1} ---")
                print(f"ID: {point.id}")
                
                if point.payload:
                    print(f"Payload keys: {list(point.payload.keys())}")
                    
                    # Check for crawler-specific metadata
                    if 'file_type' in point.payload:
                        print(f"File type: {point.payload['file_type']}")
                        if point.payload['file_type'] == 'web_page':
                            crawler_points += 1
                    
                    if 'file_path' in point.payload:
                        file_path = point.payload['file_path']
                        print(f"File path: {file_path}")
                        if 'http' in str(file_path):
                            crawler_points += 1
                    
                    if '_node_content' in point.payload:
                        content = point.payload['_node_content']
                        print(f"Content preview: {content[:100]}..." if len(content) > 100 else f"Content: {content}")
                
                # Check vector fields
                if point.vector:
                    vector_fields = list(point.vector.keys())
                    print(f"Vector fields: {vector_fields}")
                    
                    # Check if we have the expected vector fields
                    has_dense = 'text-dense' in vector_fields
                    has_sparse = 'text-sparse' in vector_fields
                    print(f"Has text-dense: {has_dense}, Has text-sparse: {has_sparse}")
                    
                    if has_dense:
                        dense_vector = point.vector['text-dense']
                        print(f"Dense vector size: {len(dense_vector)}")
                    
                    if has_sparse:
                        sparse_vector = point.vector['text-sparse']
                        print(f"Sparse vector indices count: {len(sparse_vector.indices)}")
            
            print(f"\nSummary:")
            print(f"Total points: {len(points[0])}")
            print(f"Crawler points detected: {crawler_points}")
            
            return len(points[0]) > 0 and crawler_points > 0
            
        except Exception as e:
            print(f"Error scrolling points: {e}")
            return False
            
    except Exception as e:
        print(f"Error connecting to Qdrant: {e}")
        return False

async def test_vector_search():
    """Test a simple vector search"""
    print("\n" + "=" * 60)
    print("TESTING VECTOR SEARCH")
    print("=" * 60)
    
    try:
        client = AsyncQdrantClient(url="http://localhost:6333", timeout=300)
        
        # Create a simple test vector (zeros for simplicity)
        # In a real scenario, this would be an embedding of the query
        test_vector = [0.0] * 384  # Assuming 384-dimensional embeddings
        
        # Search using the dense vector
        search_result = await client.search(
            collection_name="mainCollection",
            query_vector=("text-dense", test_vector),
            limit=5,
            with_payload=True
        )
        
        print(f"Vector search returned {len(search_result)} results")
        
        for i, result in enumerate(search_result):
            print(f"\n--- Result {i+1} ---")
            print(f"Score: {result.score}")
            if result.payload:
                if 'file_path' in result.payload:
                    print(f"File path: {result.payload['file_path']}")
                if '_node_content' in result.payload:
                    content = result.payload['_node_content']
                    print(f"Content: {content[:100]}...")
        
        return len(search_result) > 0
        
    except Exception as e:
        print(f"Error in vector search: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main debug function"""
    print("SIMPLE QDRANT DEBUG SCRIPT")
    print("=" * 60)
    
    # Run tests
    results = {}
    
    results['data_access'] = await test_qdrant_data()
    results['vector_search'] = await test_vector_search()
    
    # Summary
    print("\n" + "=" * 60)
    print("DEBUG RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    if results['data_access']:
        print("\n✓ Crawler data found in Qdrant!")
        if results['vector_search']:
            print("✓ Vector search is working!")
            print("\n🎉 The issue might be in the LlamaIndex configuration.")
            print("   Try restarting your application to pick up the vector field name changes.")
        else:
            print("⚠️  Vector search failed. Check vector field names.")
    else:
        print("\n⚠️  No crawler data found in Qdrant.")
        print("   Make sure you've run the crawler to ingest some data first.")

if __name__ == "__main__":
    asyncio.run(main())
