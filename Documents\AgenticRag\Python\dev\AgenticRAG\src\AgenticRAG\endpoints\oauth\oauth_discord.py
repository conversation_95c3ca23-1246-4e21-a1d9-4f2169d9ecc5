from imports import *

from endpoints.oauth_endpoint import OAuth2App

class OAuth2Discord(OAuth2App):
    def __init__(self, myname: str):
        super().__init__()
        self.setup(myname)

    def setup(self, myname):
        self.create_oauth(myname, "comm", ["bot", "applications.commands"], DISCORD_BOT_CLIENT_ID, DISCORD_BOT_CLIENT_SECRET, "https://discord.com/oauth2/authorize?permissions=274877975616", "https://discord.com/api/oauth2/token") \
            .set_commands(["exit"])

    async def on_fail_return(self) -> str:
        # Wordt getoond zodra de koppeling gefaald is
        ret_html = await super().on_fail_return()
        ret_html += ""
        return ret_html
    
    async def on_fail_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_fail_execute klaar is
        ret_html = await super().on_fail_execute()
        ret_html += ""
        return ret_html

    async def on_success_return(self) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return()
        ret_html += """Server wordt herstart. Geef me even 5 minuutjes en probeer vervolgens <a href='{Globals.get_endpoint_address() + "dashboard"}'>hier</a> opnieuw in te loggen"""
        return ret_html

    async def on_success_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute()
        if Globals.is_docker():
            async def delayed_exit():
                from asyncio import sleep
                await sleep(15)
                exit()

            from asyncio import create_task
            create_task(delayed_exit())
        ret_html += """Server wordt herstart. Geef me even 5 minuutjes en probeer vervolgens <a href='{Globals.get_endpoint_address() + "dashboard"}'>hier</a> opnieuw in te loggen"""
        return ret_html
