from imports import *

from qdrant_client import Qdrant<PERSON>lient, AsyncQdrantClient, models

from managers.manager_retrieval import RetrievalManager

class QDrantManager:
    _instance = None
    _initialized = False

    _client: QdrantClient = None
    _aclient: AsyncQdrantClient = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance.CreateQDrantClient()
        instance.CreateQDrantAsyncClient()
        instance._initialized = True

    @classmethod
    def GetClient(cls) -> QdrantClient:
        instance = cls.get_instance()
        if instance._client is None:
            instance.CreateQDrantClient()
        return instance._client

    @classmethod
    def GetAsyncClient(cls) -> AsyncQdrantClient:
        instance = cls.get_instance()
        if instance._aclient is None:
            instance.CreateQDrantAsyncClient()
        return instance._aclient

    def CreateQDrantClient(self) -> QdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._client = QdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._client

    def CreateQDrantAsyncClient(self) -> AsyncQdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._aclient = AsyncQdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._aclient

    @classmethod
    async def upsert(cls, id: str, data_as_str: str, metadata: dict = None, insert_node = True):
        """
        Upsert a document into Qdrant with rich metadata support.

        Args:
            id: Unique identifier for the document
            text_dense: Dense vector embedding
            text_sparse: Sparse vector embedding (BM25)
            data_as_str: The text content of the document
            metadata: Dictionary containing document metadata (file_path, file_name, file_type, etc.)
            collection_name: Name of the Qdrant collection

        The metadata typically includes:
        - file_path: Full path to the source file
        - file_name: Just the filename
        - file_type: MIME type of the file (e.g., 'text/plain', 'application/pdf')
        - file_size: Size in bytes
        - creation_date: When the file was created
        - last_modified_date: When the file was last modified
        """
        text_dense = await RetrievalManager.get_embeddings_dense(data_as_str)
        text_sparse = await RetrievalManager.get_embeddings_sparse(data_as_str)
        sparse_vector = models.SparseVector(
            indices=text_sparse.indices,
            values=text_sparse.values
        )
        from llama_index.core.schema import TextNode
        node = TextNode(
            id_=id,
            text=data_as_str,
            metadata=metadata,
            embedding=text_dense,
            embedding_sparse=sparse_vector,
        )
        if insert_node == True:
            Globals.get_index().insert_nodes([node])
        else:
            return node

    @classmethod
    async def upsert_multiple(cls, ids: list[str], data_as_strs: list[str], metadatas: list[dict]):
        nodes = []
        counter = 0
        for id in ids:
            node = await cls.upsert(id, data_as_strs[counter], metadata=metadatas[counter], insert_node=False)
            nodes.append(node)
            counter += 1
        Globals.get_index().insert_nodes(nodes)
