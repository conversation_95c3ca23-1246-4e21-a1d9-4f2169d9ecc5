from imports import *

from uuid import uuid4, UUID
import asyncio

from userprofiles.permission_levels import PERMISSION_LEVELS
from pydantic import BaseModel, Field, ConfigDict
from unstructured.partition.auto import partition

from endpoints.mybot_generic import MyBot_Generic

class ZairaUser(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaTask import LongRunningZairaTask

    username: str = ""
    rank: PERMISSION_LEVELS = PERMISSION_LEVELS.NONE
    real_name: str = ""
    email: str = ""

    # Internal
    GUID: UUID = Field(None, exclude=True)
    DeviceGUID: UUID = Field(None, exclude=True)
    sessionGUID: UUID = Field(None, exclude=True)
    my_task: "LongRunningZairaTask" = None
    asyncio_Task: asyncio.Task = Field(None, exclude=True)
    chat_history: list[str] = []
    
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, username: str, rank: PERMISSION_LEVELS, guid: UUID, device_guid: UUID):
        super().__init__()
        self.username = username
        self.rank = rank
        self.GUID = guid
        self.DeviceGUID = device_guid
        self.sessionGUID = uuid4()
        self.my_task = None
        print("ZairaUser created")
        LogFire.log("User", f"User created with GUID {self.GUID}", f". Username: {self.username}, rank: {self.rank}")
    
    def getAvailableVectorStores(self):
        match self.rank:
            case PERMISSION_LEVELS.NONE:
                return None
            case PERMISSION_LEVELS.GUEST:
                return None
            case PERMISSION_LEVELS.USER:
                return ["user_vectors"]  # Example return value
            case PERMISSION_LEVELS.ADMIN:
                return ["user_vectors", "admin_vectors"]  # Example return value
            case _:
                return None  # Default case

    async def on_message(self, complete_message: str, calling_bot: MyBot_Generic, attachments: list = [], original_message = None):
        # Convert attachments to markdown
        unstructured = ""
        for attachment in attachments:
            unstructured += partition(filename=attachment) + "\n\n\n\n\n"
        
        if len(attachments) > 0:
            complete_message += "\nAttachments:" + "\n".join(attachments)
        if self.my_task == None:
            await self.start_task(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message)
        else: 
            await self.my_task.on_message(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message)
        self.chat_history.append(f"User: {complete_message}")
    
    async def start_task(self, complete_message: str, calling_bot: MyBot_Generic, original_message = None):
        task_id: UUID = uuid4()
        self.my_task = LongRunningZairaTask(user=self, task_id=task_id, complete_message=complete_message, calling_bot=calling_bot, chat_history_on_start=self.chat_history, original_message=original_message)
        # Runs a new thread
        try:
            if Globals.get_debug() == True and calling_bot.parent_instance == None:
                await self.my_task.run_task()
            else:
                self.asyncio_Task = asyncio.create_task(self.my_task.run_task())
                self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            # Tell the system that we'd like to get the result back through the output supervisor
            await self.my_task.await_status_complete(wait_on_complete=False)
        except Exception as e:
            etc.helper_functions.exception_triggered(e, "INIT")
            await calling_bot.send_reply("Zaira heeft haar best gedaan maar is tot de conclusie gekomen dat ze hulp nodig heeft. Indien nodig stuur dan een <NAME_EMAIL>", self, self.original_physical_message)
            self.my_task = None

from userprofiles.LongRunningZairaTask import LongRunningZairaTask
ZairaUser.model_rebuild()
