from imports import *

from slack_sdk.web.async_client import Async<PERSON>eb<PERSON><PERSON>
from slack_sdk.socket_mode.aiohttp import SocketMode<PERSON>lient
from slack_sdk.web.async_client import Async<PERSON>eb<PERSON>lient
from slack_sdk.socket_mode.request import SocketModeRequest
from slack_sdk.socket_mode.response import SocketModeResponse

from endpoints.oauth_endpoint import OAuth2Verifier

class MySlackBot:
    _instance = None
    _initialized = False
    socket_mode_client: SocketModeClient = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "MySlackBot":
        return cls()
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        try:
            instance = cls.get_instance()

            instance._initialized = True

        except Exception as error:
            etc.helper_functions.exception_triggered(error, "INIT")

    @classmethod
    async def late_setup(cls):
        instance = cls.get_instance()
        bot_token = "*********************************************************"#OAuth2Verifier.get_token("slack")
        
        if bot_token:
            instance.client = AsyncWebClient(token=bot_token)
            instance.socket_mode_client = SocketModeClient(
                app_token=SLACK_BOT_APP_TOKEN,
                web_client=instance.client,
            )

            async def handle_socket_mode_request(req: SocketModeRequest):
                print("Received request:", req.type)
                print("Payload:", req.payload)
                if req.type == "events_api":
                    event = req.payload["event"]
                    print("Event type:", event.get("type"))
                    print("Event contents:", event)
                    if event.get("type") == "app_mention":
                        await instance.respond(event)

                    await instance.socket_mode_client.send_socket_mode_response(
                        SocketModeResponse(envelope_id=req.envelope_id)
                    )

            # Register the handler
            instance.socket_mode_client.socket_mode_request_listeners.append(handle_socket_mode_request)

            print("Starting Slack Bot Socket Mode client...")
            await instance.socket_mode_client.connect()
        else:
            print("Slack Bot not started - missing token")

    async def respond(self, event):
        channel_id = event['channel']
        user = event.get('user', 'unknown_user')
        thread_ts = event.get('ts', None)

        try:
            await self.client.chat_postMessage(
                channel=channel_id,
                text=f"Hello, <@{user}>! :wave:",
                thread_ts=thread_ts,
            )
        except Exception as e:
            print(f"Error sending message: {e}")
