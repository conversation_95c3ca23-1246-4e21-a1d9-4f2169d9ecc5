from imports import *

import asyncio
import requests
from xml.etree import ElementTree
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timezone
from urllib.parse import urlparse
from dotenv import load_dotenv
from crawl4ai import Async<PERSON>ebCrawler, <PERSON>rowserConfig, CrawlerRunConfig, CacheMode
import uuid

from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager

@dataclass
class ProcessedChunk:
    file_path: str  # URL for web pages
    file_name: str  # Derived from URL
    file_type: str  # "web_page"
    file_size: int  # Content length
    creation_date: str  # Crawl timestamp
    last_modified_date: str  # Crawl timestamp
    _node_content: str  # The actual chunk text content
    chunk_number: int
    title: str
    summary: str
    metadata: Dict[str, Any]
    embedding: List[float]

class Crawler:
    def __init__(self, collection_name: str = "mainCollection"):
        load_dotenv()
        self.collection_name = collection_name
        self.qdrant_client = QDrantManager.GetClient()
        self.browser_config = None
        self.crawler = None
        self.vector_size = EMBEDDING_SIZE

        # Use the configured embedding size
        print(f"Using embedding size: {self.vector_size}")

    @classmethod
    async def setup_async(cls):
        """Initialize the Crawler class asynchronously"""
        print("=" * 50)
        print("Crawler Setup Started")
        print("=" * 50)
        instance = cls()
        success = await instance.test_initialization_async()  # Changed to async version
        if success:
            print("Crawler setup completed successfully")
        else:
            print("Crawler setup completed with warnings")
        print("=" * 50)
        return instance

    async def test_initialization_async(self):
        Globals.get_debug()
        """Test the crawler initialization asynchronously"""
        try:
            # Initialize the crawler
            if not self.crawler:
                self.browser_config = BrowserConfig()
                self.crawler = AsyncWebCrawler(config=self.browser_config)
                await self.crawler.start()

            # Test that crawler is properly initialized
            if self.crawler:
                print("✓ Crawler initialization successful")
                return True
            else:
                print("✗ Crawler initialization failed")
                return False

        except Exception as e:
            print(f"✗ Crawler initialization error: {e}")
            return False

    async def start_crawler(self):
        """Initialize the crawler if not already initialized"""
        if not self.crawler:
            self.browser_config = BrowserConfig()
            self.crawler = AsyncWebCrawler(config=self.browser_config)
            await self.crawler.start()

    async def close_crawler(self):
        """Close the crawler properly"""
        if self.crawler:
            try:
                await self.crawler.close()  # Try close() as fallback
            except AttributeError:
                try:
                    await self.crawler.stop()
                except AttributeError:
                    print("Warning: Could not find proper close method for crawler")
            except Exception as e:
                print(f"Error closing crawler: {e}")
            finally:
                self.crawler = None

    def clean_text(self, text: str) -> str:
        """Clean up the text by removing excessive repetition and unwanted elements."""
        lines = text.split('\n')
        unique_lines = []
        seen = set()

        for line in lines:
            # Clean the line
            clean_line = line.strip()
            # Skip empty lines
            if not clean_line:
                continue
            # Skip image references and navigation elements
            if clean_line.startswith('![') or '→' in clean_line:
                continue
            # Hash the content to detect duplicates
            line_hash = hash(clean_line)
            if line_hash not in seen:
                seen.add(line_hash)
                unique_lines.append(line)

        # Rejoin the deduplicated text
        return '\n'.join(unique_lines)

    async def process_chunk(self, chunk: str, chunk_number: int, url: str) -> ProcessedChunk:
        """Process a single chunk of text with duplicate detection."""
        # Skip chunks that are just repetitive content
        if chunk.count(chunk[:50]) > 2:  # If the first 50 chars repeat more than twice
            print(f"Skipping repetitive chunk {chunk_number} from {url}")
            return None

        # Parse URL to get file name
        parsed_url = urlparse(url)
        file_name = parsed_url.path.split('/')[-1] if parsed_url.path.split('/')[-1] else parsed_url.netloc

        # Generate simple title and summary without LLM
        title = f"Content from {parsed_url.path} - Chunk {chunk_number}"
        summary = chunk[:200] + "..." if len(chunk) > 200 else chunk

        # Get embedding using standardized RetrievalManager method
        embedding = await RetrievalManager.get_embeddings_dense(chunk)

        # Create crawl timestamp
        crawl_timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d")

        # Create metadata for additional fields
        metadata = {
            "source": "web_crawl",
            "chunk_size": len(chunk),
            "crawled_at": str(datetime.now(timezone.utc).date()),
            "url_path": parsed_url.path,
            "chunk_index": chunk_number,
            "total_chunks": None  # Will be set later when we know total count
        }

        return ProcessedChunk(
            file_path=url,  # URL serves as file_path for web pages
            file_name=file_name,  # Derived from URL
            file_type="web_page",  # Standard type for web content
            file_size=len(chunk.encode('utf-8')),  # Content length in bytes
            creation_date=crawl_timestamp,  # Crawl timestamp
            last_modified_date=crawl_timestamp,  # Crawl timestamp
            _node_content=chunk,  # The actual chunk text content
            chunk_number=chunk_number,
            title=title,
            summary=summary,
            metadata=metadata,
            embedding=embedding
        )

    async def insert_chunk(self, chunk: ProcessedChunk):
        """Insert a processed chunk into Qdrant using standardized QDrantManager.upsert method."""
        try:
            # Get sparse embeddings
            embedded_sparse = await RetrievalManager.get_embeddings_sparse(chunk._node_content)

            # Create unique ID for the chunk
            chunk_id = str(uuid.uuid4())

            # Prepare metadata following the standardized structure
            metadata = {
                "file_path": chunk.file_path,
                "file_name": chunk.file_name,
                "file_type": chunk.file_type,
                "file_size": chunk.file_size,
                "creation_date": chunk.creation_date,
                "last_modified_date": chunk.last_modified_date,
                "chunk_index": chunk.chunk_number,
                "title": chunk.title,
                "summary": chunk.summary,
                "source": "web_crawl",
                "crawled_at": chunk.metadata.get("crawled_at"),
                "url_path": chunk.metadata.get("url_path"),
                "chunk_size": chunk.metadata.get("chunk_size")
            }

            # Use standardized QDrantManager.upsert method
            await QDrantManager.upsert(
                id=chunk_id,
                text_dense=chunk.embedding,
                text_sparse=embedded_sparse,
                data_as_str=chunk._node_content,  # The actual chunk text content
                metadata=metadata,
                collection_name=self.collection_name
            )

            print(f"Successfully inserted chunk {chunk.chunk_number} for {chunk.file_path}")
        except Exception as e:
            print(f"Error inserting chunk: {e}")
            raise

    async def process_and_store_document(self, url: str, markdown: str):
        """Process a document and store its chunks using upsert_multiple."""
        # Clean the text first to remove duplicates and unwanted elements
        cleaned_text = self.clean_text(markdown)

        # Split into chunks using the standardized RetrievalManager method
        chunks = await RetrievalManager.chunk_text(cleaned_text, chunk_size=1000, chunk_overlap=200)

        # Process chunks in parallel
        tasks = [
            self.process_chunk(chunk, i, url)
            for i, chunk in enumerate(chunks)
        ]
        processed_chunks = await asyncio.gather(*tasks)

        # Filter out None chunks and update total_chunks metadata
        valid_chunks = [chunk for chunk in processed_chunks if chunk is not None]
        total_chunks = len(valid_chunks)
        for chunk in valid_chunks:
            chunk.metadata["total_chunks"] = total_chunks

        # Prepare data for upsert_multiple - same pattern as manager_meltano
        chunk_ids = []
        chunk_texts = []
        chunk_metadatas = []

        for chunk in valid_chunks:
            # Create unique ID for the chunk
            chunk_id = str(uuid.uuid4())

            # Prepare metadata following the standardized structure
            metadata = {
                "file_path": chunk.file_path,
                "file_name": chunk.file_name,
                "file_type": chunk.file_type,
                "file_size": chunk.file_size,
                "creation_date": chunk.creation_date,
                "last_modified_date": chunk.last_modified_date,
                "chunk_index": chunk.chunk_number,
                "title": chunk.title,
                "summary": chunk.summary,
                "source": "web_crawl",
                "crawled_at": chunk.metadata.get("crawled_at"),
                "url_path": chunk.metadata.get("url_path"),
                "chunk_size": chunk.metadata.get("chunk_size"),
                "total_chunks": total_chunks
            }

            chunk_ids.append(chunk_id)
            chunk_texts.append(chunk._node_content)
            chunk_metadatas.append(metadata)

        # Use the same line as manager_meltano
        await QDrantManager.upsert_multiple(chunk_ids, chunk_texts, metadatas=chunk_metadatas)

        print(f"Successfully inserted {len(chunk_ids)} chunks for {url}")

    async def crawl_url(self, url: str, config: Optional[CrawlerRunConfig] = None):
        """Crawl a single URL and process it"""
        if not self.crawler:
            await self.start_crawler()

        if config is None:
            config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)

        try:
            result = await self.crawler.arun(
                url=url,
                config=config,
                session_id="session1"
            )
            if result.success:
                await self.process_and_store_document(url, result.markdown.raw_markdown)
                print(f"Successfully crawled: {url}")
                return True
            else:
                print(f"Failed: {url} - Error: {result.error_message}")
                return False
        except Exception as e:
            print(f"Error crawling {url}: {e}")
            return False

    async def crawl_parallel(self, urls: List[str], max_concurrent: int = 5):
        """Crawl multiple URLs in parallel with improved error handling."""
        if not self.crawler:
            await self.start_crawler()

        try:
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_url(url: str):
                async with semaphore:
                    try:
                        print(f"Starting crawl of: {url}")
                        success = await self.crawl_url(url)
                        if success:
                            print(f"Successfully processed: {url}")
                        else:
                            print(f"Failed to process: {url}")
                    except Exception as e:
                        print(f"Error processing {url}: {e}")

            # Create tasks for all URLs
            tasks = [process_url(url) for url in urls]

            # Process URLs in parallel
            await asyncio.gather(*tasks)

        finally:
            await self.close_crawler()

    async def crawl(self, url_or_sitemap: str, is_sitemap: bool = False, max_concurrent: int = 5):
        """Main crawl method - can crawl a single URL or a sitemap"""
        print(f"Starting Crawler on: {url_or_sitemap}")
        print(f"Mode: {'Sitemap' if is_sitemap else 'Single URL'}")

        if not self.crawler:
            await self.start_crawler()

        try:
            if is_sitemap:
                print("Fetching URLs from sitemap...")
                urls = self.get_sitemap_urls(url_or_sitemap)
                if not urls:
                    print("No URLs found in sitemap to crawl")
                    return

                print(f"Found {len(urls)} URLs to crawl from sitemap")
                await self.crawl_parallel(urls, max_concurrent)
            else:
                # Single URL
                await self.crawl_url(url_or_sitemap)
        except Exception as e:
            print(f"Error during crawl operation: {e}")
        finally:
            await self.close_crawler()

    def get_sitemap_urls(self, sitemap_url: str) -> List[str]:
        """Get URLs from a sitemap."""
        try:
            print(f"Fetching sitemap from: {sitemap_url}")
            response = requests.get(sitemap_url, timeout=30)
            response.raise_for_status()

            # Parse the XML
            root = ElementTree.fromstring(response.content)

            namespaces = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}

            urls = []
            # Try with namespace first
            locations = root.findall('.//sm:loc', namespaces)
            if not locations:
                locations = root.findall('.//loc')

            if locations:
                urls = [loc.text for loc in locations if loc.text]
                print(f"Found {len(urls)} URLs in sitemap")

            # Remove duplicates while preserving order
            urls = list(dict.fromkeys(urls))
            print(f"Total unique URLs found in sitemap: {len(urls)}")
            return urls

        except requests.RequestException as e:
            print(f"Network error fetching sitemap: {e}")
        except ElementTree.ParseError as e:
            print(f"XML parsing error: {e}")
        except Exception as e:
            print(f"Unexpected error: {e}")
        return []
