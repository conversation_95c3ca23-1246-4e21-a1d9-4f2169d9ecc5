from imports import *

from endpoints.oauth_endpoint import OAuth2App

class OAuth2Discord(OAuth2App):
    def __init__(self, myname: str):
        super().__init__()
        self.setup(myname)

    def setup(self, myname):
        self.create_oauth(myname, "input", [""], "client_id", "client_secret", "auth_url", "token_url")

    async def on_fail_return(self) -> str:
        # Wordt getoond zodra de koppeling gefaald is
        ret_html = await super().on_fail_return()
        ret_html += ""
        return ret_html

    async def on_fail_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_fail_execute klaar is
        ret_html = await super().on_fail_return()
        ret_html += ""
        return ret_html

    async def on_success_return(self) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return()
        ret_html += ""
        return ret_html

    async def on_success_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute()
        ret_html += ""
        return ret_html
