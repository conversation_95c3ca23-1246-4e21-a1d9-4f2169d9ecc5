from imports import *

from os import path as os_path
from typing import Optional
from json import dump as json_dump
from aiohttp import web, ClientSession
from aiohttp_oauth2_client.grant.authorization_code import AuthorizationCodeGrant
from urllib.parse import urlencode
from typing import TYPE_CHECKING, Literal
from datetime import datetime, timezone
from os import getcwd

from managers.manager_postgreSQL import PostgreSQLManager
from endpoints.oauth_commands import oauth_handle_commands

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from aiohttp_oauth2_client.client import OAuth2Client

class OAuth2App:
    identifier: str = ""
    scopes: list[str] = []
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    auth_url: Optional[str] = None
    token_url: Optional[str] = None
    meltano_env: dict = {}
    commands: list[str] = []
    section: str = ""

    def create_input(self, identifier: str, section: Literal["comm", "input", "auto"], input_fields: list[str], admin_only: bool = False) -> "OAuth2App":
        """strings saved in order: access_token, refresh_token, token_type, str1, str2, str3, str4, str5, str6, str7.
            Integers saved in order: expires_in, refresh_token_expires_in, int1, int2, int3, int4, int5, int6, int7, int8"""
        self.identifier = identifier
        self.section = section
        self.scopes = input_fields
        return self

    def create_oauth(self, identifier: str, section: Literal["comm", "input", "auto"], scopes: list[str], client_id: str, client_secret: str, auth_url: str, token_url: str, admin_only:bool = False) -> "OAuth2App":
        self.identifier = identifier
        self.section = section
        self.scopes = scopes
        self.client_id = client_id
        self.client_secret = client_secret
        self.auth_url = auth_url
        self.token_url = token_url
        return self

    def set_meltano(self, meltano_env: dict[str, list[str]]) -> "OAuth2App":
        if isinstance(meltano_env, str):
            meltano_env = [meltano_env]
        self.meltano_env = meltano_env
        return self

    def set_commands(self, commands: list[str]) -> "OAuth2App":
        self.commands = commands
        return self
    
    async def on_fail_return(self) -> str:
        return ""
    
    async def on_fail_execute(self) -> str:
        return ""
    
    async def on_success_return(self) -> str:
        return ""
    
    async def on_success_execute(self) -> str:
        return ""

class OAuth2Verifier:
    _instance = None
    _initialized = False

    apps: dict[str, OAuth2App] = {}

    bot_tokens: dict[str, dict[str, str]] = {}
    oauth_clients: dict[str, "OAuth2Client"] = {}

    default_value = {
        "access_token": "",
        "expires_in": 0,
        "token_type": "",
        "refresh_token": "",
        "refresh_token_expires_in": 0,
        "valid": True,
        "created_at": datetime.now(timezone.utc).replace(tzinfo=None),
        "str1": "",
        "str2": "",
        "str3": "",
        "str4": "",
        "str5": "",
        "str6": "",
        "str7": "",
        "int1": 0,
        "int2": 0,
        "int3": 0,
        "int4": 0,
        "int5": 0,
        "int6": 0,
        "int7": 0,
        "int8": 0,
    }

    def instantiate(self):
        from endpoints.oauth.oauth_discord import OAuth2Discord
        from endpoints.oauth.oauth_website import OAuth2Website
        self.apps["discord"] = OAuth2Discord("discord")

        self.apps["website"] = OAuth2Website("website")
        self.apps["slack"] = OAuth2App().create_oauth("slack", "comm", ["app_mentions:read", "chat:write", "channels:read", "channels:history"], SLACK_BOT_CLIENT_ID, SLACK_BOT_CLIENT_SECRET, "https://slack.com/oauth/v2/authorize", "https://slack.com/api/oauth.v2.access")
        self.apps["gcalendar"] = OAuth2App().create_oauth("gcalendar", "input", ["https://www.googleapis.com/auth/calendar.events.freebusy", "https://www.googleapis.com/auth/calendar.events"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gmail"] = OAuth2App().create_oauth("gmail", "input", ["https://www.googleapis.com/auth/gmail.modify"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gdrive"] = OAuth2App().create_oauth("gdrive", "input", ["https://www.googleapis.com/auth/drive","https://www.googleapis.com/auth/drive.readonly"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                    .set_meltano({"GDRIVE_ACCESS_TOKEN": "access_token", "GDRIVE_REFRESH_TOKEN": "refresh_token"})
        self.apps["googleads"] = OAuth2App().create_oauth("googleads", "input", ["https://www.googleapis.com/auth/adwords"], GOOGLE_ADS_BOT_CLIENT_ID, GOOGLE_ADS_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                    .set_meltano({"TAP_GOOGLEADS_OAUTH_CREDENTIALS": "full_token", "TAP_GOOGLEADS_OAUTH_CREDENTIALS_REFRESH_TOKEN": "refresh_token"})
        self.apps["airtable"] = OAuth2App().create_input("airtable", "input", "Access Token") \
                                    .set_meltano({"TAP_AIRTABLE_TOKEN": "access_token"})
        self.apps["imap"] = OAuth2App().create_input("imap", "input", ["Server Naam", "Netwerk port", "E-mail adres", "E-mail wachtwoord"])
        self.apps["smtp"] = OAuth2App().create_input("smtp", "auto", ["Server Naam", "Netwerk port", "E-mail adres", "E-mail wachtwoord"])
        self.apps["debug"] = OAuth2App().create_input("debug", "auto", ["Debug-Modus? (ja/nee)", "Deel bedrijfsdata? (ja/nee)"], True) \
                                    .set_commands(["debug"])
        self.apps["woocommerce"] = OAuth2App().create_input("woocommerce", "input", ["Consumer Key", "Consumer Secret", "WooCommerce URL", "Data verzamelen vanaf (YYYY-MM-DD)"]) \
                                    .set_meltano({
                                        "TAP_WOOCOMMERCE_CONSUMER_KEY": "access_token",
                                        "TAP_WOOCOMMERCE_CONSUMER_SECRET": "refresh_token",
                                        "TAP_WOOCOMMERCE_SITE_URL": "token_type",
                                        "TAP_WOOCOMMERCE_START_DATE": "str1"
                                    }) \
                                    .set_commands(["extract_woocommerce"])
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "OAuth2Verifier":
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        try:
            instance.instantiate()
            from endpoints.api_endpoint import APIEndpoint
            from aiohttp_oauth2_client.client import OAuth2Client

            for identifier, app in instance.apps.items():
                if app.client_id and app.client_secret:
                    # Create an OAuth2 Client if the bot keys are provided. Without them we want a html page for user input
                    myscope = " ".join(app.scopes)

                    redirect_url = (f"http://localhost:8084" if Globals.get_debug() else "https://oauth.askzaira.com") + f"/oauth_redirect"
                    network_name = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "") if Globals.is_docker() else "python"
                    params = {
                        "client_id": app.client_id,
                        "redirect_uri": redirect_url,
                        "scope": myscope,
                        "state": f"{network_name}-{identifier}", # TODO: Needs improved
                        "response_type": "code",
                        "access_type": "offline",
                        "prompt": "consent",
                    }
                    grant = AuthorizationCodeGrant(
                        client_id=app.client_id,
                        client_secret=app.client_secret,
                        token_url=f"{app.token_url}",
                        authorization_url=app.auth_url + ("&" if "?" in app.auth_url else "?") + urlencode(params),
                        scope=myscope,
                    )
                    client = OAuth2Client(
                        grant=grant,
                    )
                    instance.oauth_clients[identifier] = client
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.get(f"/{identifier}/oauth", instance.oauth)
                    ])
                else:
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.get(f"/{identifier}/oauth", instance.oauth),
                        web.post(f"/{identifier}/oauth_redirect", instance.oauth_redirect_post),
                    ])
                APIEndpoint.get_instance().aio_app.add_routes([
                    web.get(f"/oauth_redirect", instance.oauth_redirect_get),
                ])

            instance._initialized = True

        except Exception as error:
            etc.helper_functions.exception_triggered(error, "INIT")

    @classmethod
    async def get_token(cls, identifier: str, token_key: str = "access_token") -> str:
        instance = cls.get_instance()
        token = await instance.get_full_token(identifier)
        if token:
            if token_key == "full_token":
                return token
            return token[token_key]
        return ""

    @classmethod
    async def get_full_token(cls, identifier: str) -> Optional[dict[str]]:
        instance = cls.get_instance()

        if identifier in instance.bot_tokens:
            return instance.bot_tokens[identifier]

        await PostgreSQLManager.connect_to_database("vectordb")
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM oauth WHERE identifier = $1 ORDER BY created_at DESC LIMIT 1;", [identifier])
        if result:
            row = result[0]
            token = {
                "access_token": row["access_token"],
                "expires_in": row["expires_in"],
                "token_type": row["token_type"],
                "refresh_token": row["refresh_token"],
                "refresh_token_expires_in": row["refresh_token_expires_in"],
                "str1": row["str1"],
                "str2": row["str2"],
                "str3": row["str3"],
                "str4": row["str4"],
                "str5": row["str5"],
                "str6": row["str6"],
                "str7": row["str7"],
                "int1": row["int1"],
                "int2": row["int2"],
                "int3": row["int3"],
                "int4": row["int4"],
                "int5": row["int5"],
                "int6": row["int6"],
                "int7": row["int7"],
                "int8": row["int8"],
                "valid": row["valid"],
                "created_at": datetime.now(timezone.utc)
            }
            instance.bot_tokens[identifier] = token
            return instance.bot_tokens[identifier]

        return None

    @classmethod
    def get_client(cls, identifier: str) -> "OAuth2Client":
        return cls.get_instance().oauth_clients[identifier]

    async def save_tokens(self, identifier, tokens):
        ret_val = True
        tokens["valid"] = True
        self.bot_tokens[identifier] = tokens
        await PostgreSQLManager.connect_to_database("vectordb")
        query = """
        INSERT INTO oauth (
            identifier,
            access_token,
            expires_in,
            token_type,
            refresh_token,
            refresh_token_expires_in,
            str1,
            str2,
            str3,
            str4,
            str5,
            str6,
            str7,
            int1,
            int2,
            int3,
            int4,
            int5,
            int6,
            int7,
            int8,
            valid,
            created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
        ON CONFLICT (identifier)
        DO UPDATE SET
            access_token = EXCLUDED.access_token,
            expires_in = EXCLUDED.expires_in,
            token_type = EXCLUDED.token_type,
            refresh_token = EXCLUDED.refresh_token,
            refresh_token_expires_in = EXCLUDED.refresh_token_expires_in,
            str1 = EXCLUDED.str1,
            str2 = EXCLUDED.str2,
            str3 = EXCLUDED.str3,
            str4 = EXCLUDED.str4,
            str5 = EXCLUDED.str5,
            str6 = EXCLUDED.str6,
            str7 = EXCLUDED.str7,
            int1 = EXCLUDED.int1,
            int2 = EXCLUDED.int2,
            int3 = EXCLUDED.int3,
            int4 = EXCLUDED.int4,
            int5 = EXCLUDED.int5,
            int6 = EXCLUDED.int6,
            int7 = EXCLUDED.int7,
            int8 = EXCLUDED.int8,
            valid = EXCLUDED.valid,
            created_at = EXCLUDED.created_at;
        """
        params = [
            identifier,
            tokens['access_token'],
            tokens['expires_in'],
            tokens['token_type'],
            tokens['refresh_token'],
            tokens['refresh_token_expires_in'],
            tokens['str1'],
            tokens['str2'],
            tokens['str3'],
            tokens['str4'],
            tokens['str5'],
            tokens['str6'],
            tokens['str7'],
            tokens['int1'],
            tokens['int2'],
            tokens['int3'],
            tokens['int4'],
            tokens['int5'],
            tokens['int6'],
            tokens['int7'],
            tokens['int8'],
            tokens['valid'],
            tokens['created_at'],
        ]
        await PostgreSQLManager.execute_query("vectordb", query, params)
        await PostgreSQLManager.close_connection("vectordb")
        # makedirs('./tokens', exist_ok=True)
        # with open(f'./tokens/{identifier}.json', "w") as f:
        #     json_dump(tokens, f)


        ret_val = ret_val & await self.save_to_env(identifier, tokens, "/meltano/project/.env")
        ret_val = ret_val & await self.save_to_env(identifier, tokens, "/app/.env")
        ret_val = ret_val & await oauth_handle_commands(self, self.apps[identifier], tokens)
        return ret_val

    async def save_to_env(self, identifier, tokens, env_path):
        ret_val = True
        # Add the token value to the .env file=
        try:
            # Determine the actual env file path based on environment
            actual_env_path = env_path
            if not Globals.is_docker():
                # For local development, use the relative path to meltano .env file
                if env_path == "/meltano/project/.env":
                    actual_env_path = getcwd() + "/src/meltano/askzaira/.env"
                elif env_path == "/app/.env":
                    actual_env_path = getcwd() + "/.env"  # Local .env file in AgenticRAG directory

            print(f"Saving {identifier} credentials to: {actual_env_path}")

            # Read existing lines (if the file exists)
            lines = []
            if os_path.exists(actual_env_path):
                with open(actual_env_path, "r") as f:
                    lines = f.readlines()

            app = self.apps[identifier]
            if len(app.meltano_env) > 0:
                for key, value in app.meltano_env.items():
                    if value == "full_token":
                        new_token = tokens
                    else:
                        new_token = tokens[value]
                    env_var_key = key

                    print(f"Setting {env_var_key}={new_token}")

                    # Update or add the env variable
                    updated = False
                    for i, line in enumerate(lines):
                        if line.startswith(f"{env_var_key}="):
                            lines[i] = f"{env_var_key}={new_token}\n"
                            updated = True
                            break

                    if not updated:
                        lines.append(f"{env_var_key}={new_token}\n")

            # Write the updated lines back
            with open(actual_env_path, "w") as f:
                f.writelines(lines)

            print(f"Successfully saved {identifier} credentials to {actual_env_path}")

        except Exception as e:
            print(f"Error saving to env file {env_path}: {e}")
            ret_val = False

        return ret_val

    async def invalidate_tokens(self, identifier):
        tokens = await self.get_full_token(identifier)
        tokens["valid"] = False
        with open(f'./tokens/{identifier}.json', "w") as f:
            json_dump(tokens, f)

    async def load_content_oauth_input_fields(self, request: web.Request):
        # <form action="/{identifier}/oauth_redirect" method="post">
        # <div class="form-row">
        identifier = request.rel_url.parts[1]
        app = self.apps[identifier]
        html_content = f"""
            <div class="card-header">
                <h2 class="card-title">Server Configuratie</h2>
                <p class="card-subtitle">Voer je {identifier.upper()} instellingen in om door te gaan naar het dashboard</p>
            </div>
            <form class="form-container" id="setupForm">
                <input type="hidden" id="identifier" name="identifier" value="{identifier}">"""
        for scope in app.scopes:
                    html_content += f"""
                                    <div class="form-group">
                                        <label class="form-label" for="{scope}">{scope.replace("_", " ")}</label>
                                        <input 
                                            type="{'text' if not 'wachtwoord' in scope.lower() else 'password'}" 
                                            id="{scope}" 
                                            name="{scope}" 
                                            class="form-input" 
                                            placeholder="{""}"
                                            {"" if ("Optional" in scope) else "required"}
                                        >
                                        <div class="form-help">{""}</div>
                                        <div class="form-error" id="serverNameError">Vul een geldige waarde in</div>
                                    </div>
                                    """
        # Temporarily add a password verification
        html_content += f"""
                        <div class="form-group">
                            <label class="form-label" for="username" style="display: none;"></label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="form-input"
                                autocomplete="username"
                                style="position: absolute; left: -9999px; top: -9999px;" 
                                tabindex="-1"
                                aria-hidden="true"
                            >
                            <div class="form-help">{""}</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="userpass">Bevestig met gebruikers wachtwoord</label>
                            <input 
                                type="{'password'}" 
                                id="userpass" 
                                name="userpass" 
                                class="form-input" 
                                {"required"}
                            >
                            <div class="form-help">{""}</div>
                            <div class="form-error" id="serverNameError">Vul een geldige waarde in</div>
                        </div>
                        """
        
        html_content += """
                    <button type="button" onclick="window.history.back();" class="back-button" id="cancelButton">
                        Annuleer
                    </button>
                    <button type="submit" class="submit-button" id="submitButton">
                        <span class="loading"></span>
                        <span class="button-text">Configuratie Voltooien</span>
                    </button>
                </form>
        """
        return html_content

    async def oauth(self, request: web.Request):
        print(f"Endpoint oauth called from IP: {request['real_ip']}")
        identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        if not client:
            content = await self.load_content_oauth_input_fields(request)
            site = etc.helper_functions.create_html_out("inputfield", content)
            return web.Response(text=site, content_type="text/html")

        authorization_url = client.grant.authorization_url
        #return web.Response(status=302,headers={'Location':str(authorization_url)})

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Redirecting...</title>
            <script>
                // Redirect immediately
                window.location.href = "{authorization_url}";
            </script>
        </head>
        <body>
            <p>Token aanvragen... Klik <a href='{authorization_url}'>hier</a> als dit niet automatisch laadt.</p>
        </body>
        </html>
        """

        return web.Response(text=html_content, content_type='text/html')

    async def oauth_redirect_post(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        try:
            if request.content_type == 'application/json':
                data = await request.json()
            else:
                data = await request.post()
        except Exception:
            return web.json_response({"error": "Invalid Post", "success": False}, status=400)
        
        # Temporary until user login remains saved
        from hashlib import md5
        username = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "") if Globals.is_docker() else "proxyhttpaio"
        md5pass = md5(("!askzaira#" + username + "-askzaira=").encode('utf-8')).hexdigest()
        datapass = data.get('userpass')
        if md5pass != datapass:
            return web.json_response({"error": "Invalid Post", "success": False}, status=400)

        app = self.apps[identifier]
        string_id = 0
        int_id = 0
        save_value = self.default_value
        token_int_values = ["expires_in", "refresh_token_expires_in", "int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]
        token_string_values = ["access_token", "refresh_token", "token_type", "str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        from re import fullmatch
        for scope in app.scopes:
            value = data.get(scope.replace(" ", ""))
            if (not "Optional" in scope) and not value:
                return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)

            if fullmatch(r"-?\d+", value):  # Matches optional minus sign and digits
                save_value[token_int_values[int_id]] = int(value)
                int_id += 1
            else:
                save_value[token_string_values[string_id]] = value
                string_id += 1

        if await self.save_tokens(identifier, save_value):
            return web.json_response({"success": True}, status=200)
            return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
        else:
            return web.json_response({"success": True}, status=200)
            return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_restart")
        return web.Response(text="Authenticatie successvol! Tokens beveiligd. Dit tabblad mag afgesloten worden.")

    async def oauth_redirect_get(self, request: web.Request):
        if "state" in request.query:
            identifier = request.query.get("state").split("-", 1)[1]
        else:
            identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        app = self.apps[identifier]
        if not client:
                html_content = etc.helper_functions.create_html_out("inputfield", f"""OAuth2 client not found for {identifier}. Klik <a href='{Globals.get_endpoint_address() + "dashboard"}'>hier</a> om het opnieuw te proberen.""")
                return web.Response(text=html_content, type="text/html", status=404)
        try:
            # Currently as first, probably should be inside the 'not code or not scope' if statement
            if request.can_read_body == True and request.text() != "":
                response_data = await request.json()
                token = self.default_value | response_data["token"]
                if not token:
                    return web.Response(text="Token not received", status=400)

                self.bot_tokens[identifier] = {"access_token":token}
                await self.save_tokens(identifier, self.bot_tokens[identifier])

                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
            else:
                code = request.query.get("code", "")
                scope = request.query.get("scope", "")
                permissions = request.query.get("permissions") # Currently Discord-specific. Code needs expanded if more than 1 OAuth fails to deliver the scope
                if not code or (not scope and not permissions):
                    html_content = etc.helper_functions.create_html_out("inputfield", f"""Error: Geen code of scope meeverstuurd. Klik <a href='{Globals.get_endpoint_address() + "dashboard"}'>hier</a> om het opnieuw te proberen.""")
                    return web.Response(text=html_content, content_type='text/html')
                redirect_url = (f"http://localhost:8084" if Globals.get_debug() else f"https://oauth.askzaira.com") + f"/oauth_redirect"
                async with ClientSession() as session:
                    async with session.post(
                        app.token_url,
                        data={
                            "grant_type": "authorization_code",
                            "code": code,
                            "redirect_uri": redirect_url,
                            "client_id": app.client_id,
                            "client_secret": app.client_secret,
                        }
                    ) as resp:
                        html_content = f"""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8" /><title>Er is iets fout gegaan...</title></head><body>
                                        <p>Authenticatie successvol binnengekomen maar scopes niet kunnen verifiëren. Klik <a href='{Globals.get_endpoint_address() + 'dashboard'}'>hier</a> om het opnieuw te proberen.</p>
                                        </body></html>"""
                        if resp.status != 200:
                            error_text = await resp.text()
                            print(f"Token request failed: {error_text}")
                            return web.Response(text=error_text, status=resp.status, content_type='application/json')
                        tokens = await resp.json()
                        # Check if all scopes have been granted
                        if (set(tokens["scope"].split()) == set(scope.split()) and set(tokens["scope"].split()) == set(self.apps[identifier].scopes)) or permissions == "274877975616":
                            guild_id = request.query.get("guild_id", "") # Specific to Discord
                            if guild_id != "":
                                tokens["token_type"] = guild_id
                                #await self.save_to_env("discord_guild_id", {"discord_guild_id": guild_id}, "/app/.env") # Only works from within Docker
                            save_value = {
                                "access_token": tokens["access_token"],
                                "expires_in": tokens["expires_in"],
                                "token_type": tokens["token_type"],
                                "refresh_token": tokens["refresh_token"],
                                "refresh_token_expires_in": tokens["refresh_token_expires_in"] if "refresh_token_expires_in" in tokens else 0,
                                "valid": True,
                                "created_at": datetime.now(timezone.utc).replace(tzinfo=None)
                            }
                            if await self.save_tokens(identifier, self.default_value | save_value):
                                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
                            else:
                                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_restart")
                        else:
                            html_content = etc.helper_functions.create_html_out("inputfield", "<p>Token aanvraag gefaald: Alle verzochte permissies moeten aangevinkt worden. Klik <a href='{Globals.get_endpoint_address() + 'dashboard'}'>hier</a> om het opnieuw te proberen.</p>")
                        return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            etc.helper_functions.handle_asyncio_task_result_errors(e)
            site = etc.helper_functions.create_html_out("inputfield", f"""OAuth error: {str(e)}. Klik <a href='{Globals.get_endpoint_address() + "dashboard"}'>hier</a> om het opnieuw te proberen.""")
            return web.Response(text=site, content_type='text/html', status=500)
