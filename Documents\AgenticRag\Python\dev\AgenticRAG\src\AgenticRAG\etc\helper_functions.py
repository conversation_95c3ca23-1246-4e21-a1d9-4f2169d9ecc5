from os import path, listdir
from typing import Literal, Optional, TYPE_CHECKING

from globals import Globals

if TYPE_CHECKING:
    from userprofiles.ZairaUser import <PERSON>air<PERSON><PERSON><PERSON>

def get_any_message_as_type():
    from langchain_core.messages import AIMessage, HumanMessage, ChatMessage, SystemMessage, FunctionMessage, ToolMessage, AIMessageChunk, HumanMessageChunk, ChatMessageChunk, SystemMessageChunk, FunctionMessageChunk, ToolMessageChunk, AnyMessage
    return (
        AIMessage,
        HumanMessage,
        ChatMessage,
        SystemMessage,
        FunctionMessage,
        ToolMessage,
        AIMessageChunk,
        HumanMessageChunk,
        ChatMessageChunk,
        SystemMessageChunk,
        FunctionMessageChunk,
        ToolMessageChunk,
    )

def folder_has_files(folder_path):
    # Check if the folder exists
    if not path.isdir(folder_path):
        print(f"The folder {folder_path} does not exist.")
        return False

    # List all entries in the directory
    entries = listdir(folder_path)
    
    # Check if any entry is a file (not a directory)
    for entry in entries:
        if path.isfile(path.join(folder_path, entry)):
            return True
    
    return False

def convert_key_value_to_string(**kwargs):
    return "\n\n".join(f"{key.replace('_', ' ')}: {value}" for key, value in kwargs.items())

def exception_triggered(e: Exception, event_code: Literal["INIT", "RETRIEVE", "TOP", "TASK", "OUTPUT"], user: Optional["ZairaUser"] = None):
    from managers.manager_logfire import LogFire
    import traceback
    print(f"Error during execution: {e}")
    trace = traceback.format_exc()
    print(trace)
    LogFire.log(event_code, trace, user, "", str(e), "error")
    if Globals.get_debug() == False:
        exit()
    else:
        breakpoint()

def handle_asyncio_task_result_errors(task):
    try:
        task.result()
    except Exception as e:
        print(f"Caught exception: {e}")

def call_cmd_debug(path, program, command):
    # Can NOT be enabled in production!!
    if Globals.is_docker() == True:
        return
    
    from subprocess import run as subprocess_run
    from subprocess import CalledProcessError

    try:
        result = subprocess_run(
            [program] + command.split(),
            capture_output=True,
            text=True,
            check=True,
            cwd=path
        )
        return result.stdout
    except CalledProcessError as e:
        print("Call_Network_Docker Error:\n", e.stderr)
        exception_triggered(e)

def call_network_docker(container, command):
    from subprocess import run as subprocess_run
    from subprocess import CalledProcessError

    network = get_value_from_env("ZAIRA_NETWORK_NAME", None)
    if not Globals.get_debug():
        try:
            result = subprocess_run(
                ["docker", "exec", network + "-" + container, container] + command.split(),
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout
        except CalledProcessError as e:
            print("Call_Network_Docker Error:\n", e.stderr)
            exception_triggered(e)
    else:
        call_cmd_debug("src/meltano/askzaira", container, command)
        if Globals.is_docker():
            print("Call_Network_Docker Error:\nNo ZAIRA_NETWORK_NAME found")

def get_value_from_env(key: str, default_value: str | list[str] = "", can_be_list=False) -> str | list[str]:
    from dotenv import load_dotenv
    from os import getenv

    # load_dotenv(dotenv_path=".env")  # Done on program start

    # Load the value from env or fall back to default
    raw_value = getenv(key)
    if raw_value is None:
        if isinstance(default_value, list):
            return default_value
        raw_value = default_value

    if can_be_list:
        # Auto-detect list
        if isinstance(raw_value, str) and "," in raw_value:
            return [item.strip() for item in raw_value.split(",") if item.strip()]
    
    return raw_value

def create_html_out(page_name: str, content: str) -> str:
    from os import getcwd
    from os import path as os_path
    if Globals.is_docker():
        ui_folder = "/app/ui/"
    else:
        ui_folder = getcwd() + "/src/AgenticRAG/ui/"

    with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
        HEAD_HTML = f.read()

    with open(os_path.join(ui_folder, page_name + "_header.txt"), "r", encoding="utf-8") as f:
        HEADER_HTML = f.read()

    with open(os_path.join(ui_folder, page_name + "_footer.txt"), "r", encoding="utf-8") as f:
        FOOTER_HTML = f.read()

    html_content = f"""<!DOCTYPE html>
            <html lang="nl">
            {HEAD_HTML}
            {HEADER_HTML}
            {content}
            {FOOTER_HTML}
            </html>"""
    return html_content